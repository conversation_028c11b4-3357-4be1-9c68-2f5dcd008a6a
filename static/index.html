<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>World of Airports Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="manifest" href="/static/manifest.json" />
    <meta name="theme-color" content="#3B82F6" />
    <link rel="icon" href="/static/icon-192.png" />
  </head>
  <body class="bg-gray-50 min-h-screen">
    <div id="app" class="max-w-md mx-auto bg-white min-h-screen shadow-lg">
      <!-- Header -->
      <header class="bg-blue-600 text-white p-4">
        <h1 class="text-xl font-bold text-center">WoA Tracker</h1>
        <div class="text-center text-sm opacity-90">
          World of Airports Game Tracker
        </div>
      </header>

      <!-- Navigation -->
      <nav class="bg-blue-500 text-white p-2">
        <div class="flex justify-around">
          <button
            onclick="showSection('dashboard')"
            class="nav-btn px-2 py-2 rounded active text-xs"
          >
            🏰 Airports
          </button>
          <button
            onclick="showSection('contracts')"
            class="nav-btn px-2 py-2 rounded text-xs"
          >
            ⏳ Pending
          </button>
          <button
            onclick="showSection('activities')"
            class="nav-btn px-2 py-2 rounded text-xs"
          >
            👥 Players
          </button>
          <button
            onclick="showSection('analytics')"
            class="nav-btn px-2 py-2 rounded text-xs"
          >
            📊 Stats
          </button>
        </div>
      </nav>

      <!-- Dashboard Section -->
      <section id="dashboard" class="section p-4">
        <h2 class="text-lg font-semibold mb-4">Airport Activity Dashboard</h2>

        <!-- Key Metrics -->
        <div class="grid grid-cols-2 gap-3 mb-6">
          <div class="bg-blue-100 p-3 rounded-lg text-center">
            <div class="text-xl font-bold text-blue-600" id="pending-contracts">
              -
            </div>
            <div class="text-xs text-gray-600">Pending Contracts</div>
          </div>
          <div class="bg-green-100 p-3 rounded-lg text-center">
            <div class="text-xl font-bold text-green-600" id="active-players">
              -
            </div>
            <div class="text-xs text-gray-600">Active Today</div>
          </div>
          <div class="bg-purple-100 p-3 rounded-lg text-center">
            <div class="text-xl font-bold text-purple-600" id="busy-airports">
              -
            </div>
            <div class="text-xs text-gray-600">Busy Airports</div>
          </div>
          <div class="bg-orange-100 p-3 rounded-lg text-center">
            <div
              class="text-xl font-bold text-orange-600"
              id="total-activities"
            >
              -
            </div>
            <div class="text-xs text-gray-600">Today's Activities</div>
          </div>
        </div>

        <!-- Airport Activity Overview -->
        <div class="bg-white border rounded-lg p-4 mb-4">
          <h3 class="font-semibold mb-3 flex items-center">
            🏰 Top Active Airports
            <span class="ml-auto text-xs bg-gray-200 px-2 py-1 rounded"
              >Live</span
            >
          </h3>
          <div id="airport-activity-list" class="space-y-2">
            <div class="text-sm text-gray-500">Loading airport activity...</div>
          </div>
          <!-- Quick Add Player Button -->
          <div class="mt-3 pt-3 border-t">
            <button
              onclick="showQuickPlayerForm()"
              class="w-full bg-green-500 text-white py-2 px-3 rounded text-sm font-medium"
            >
              ➕ Quick Add Player at Airport
            </button>
          </div>
        </div>

        <!-- Pending Contracts Overview -->
        <div class="bg-white border rounded-lg p-4 mb-4">
          <h3 class="font-semibold mb-3 flex items-center">
            ⏳ Pending Contracts
            <button
              onclick="loadPendingContracts()"
              class="ml-auto text-xs bg-blue-500 text-white px-2 py-1 rounded"
            >
              Refresh
            </button>
          </h3>
          <div id="pending-contracts-list" class="space-y-2">
            <div class="text-sm text-gray-500">
              Loading pending contracts...
            </div>
          </div>
          <!-- Quick Add Contract Button -->
          <div class="mt-3 pt-3 border-t">
            <button
              onclick="showQuickContractForm()"
              class="w-full bg-blue-500 text-white py-2 px-3 rounded text-sm font-medium"
            >
              ➕ Quick Add Contract
            </button>
          </div>
        </div>

        <!-- Recent Player Activity -->
        <div class="bg-white border rounded-lg p-4 mb-4">
          <h3 class="font-semibold mb-3 flex items-center">
            👥 Recent Player Activity
            <span class="ml-auto text-xs text-gray-500">Last 10</span>
          </h3>
          <div
            id="recent-activity-list"
            class="space-y-2 max-h-48 overflow-y-auto"
          >
            <div class="text-sm text-gray-500">Loading player activity...</div>
          </div>
        </div>

        <!-- Natural Language Query -->
        <div class="bg-white border rounded-lg p-4">
          <h3 class="font-semibold mb-3">Ask About Your Data</h3>
          <div class="flex space-x-2 mb-3">
            <input
              type="text"
              id="nl-query"
              class="flex-1 p-2 border rounded"
              placeholder="e.g., 'Show A380 contracts this week'"
            />
            <button
              onclick="startVoiceQuery()"
              class="p-2 bg-blue-500 text-white rounded"
            >
              🎤
            </button>
          </div>
          <button
            onclick="processQuery()"
            class="w-full bg-gray-500 text-white py-2 rounded"
          >
            Ask AI
          </button>
          <div id="query-result" class="mt-3 hidden">
            <div class="bg-gray-100 p-3 rounded text-sm">
              <div id="query-interpretation"></div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contracts Section -->
      <section id="contracts" class="section p-4 hidden">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Pending Contracts</h2>
          <div class="flex gap-2">
            <button
              onclick="showQuickContractForm()"
              class="bg-blue-500 text-white px-2 py-1 rounded text-xs"
            >
              ➕ Add
            </button>
            <button
              onclick="filterContracts('pending')"
              class="bg-yellow-500 text-white px-2 py-1 rounded text-xs"
              id="pending-filter"
            >
              Pending
            </button>
            <button
              onclick="filterContracts('all')"
              class="bg-gray-500 text-white px-2 py-1 rounded text-xs"
              id="all-filter"
            >
              All
            </button>
          </div>
        </div>

        <!-- Contract Status Summary -->
        <div class="grid grid-cols-3 gap-2 mb-4">
          <div class="bg-yellow-100 p-2 rounded text-center">
            <div class="text-lg font-bold text-yellow-600" id="pending-count">
              -
            </div>
            <div class="text-xs text-gray-600">Pending</div>
          </div>
          <div class="bg-green-100 p-2 rounded text-center">
            <div class="text-lg font-bold text-green-600" id="accepted-count">
              -
            </div>
            <div class="text-xs text-gray-600">Accepted</div>
          </div>
          <div class="bg-red-100 p-2 rounded text-center">
            <div class="text-lg font-bold text-red-600" id="rejected-count">
              -
            </div>
            <div class="text-xs text-gray-600">Rejected</div>
          </div>
        </div>

        <div id="contracts-list" class="space-y-3">
          <!-- Contracts will be loaded here -->
        </div>

        <!-- Contract Form Modal -->
        <div
          id="contract-modal"
          class="fixed inset-0 bg-black bg-opacity-50 hidden"
        >
          <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg p-6 w-full max-w-sm">
              <h3 class="font-semibold mb-4">Add Contract</h3>
              <form id="contract-form" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium">Plane Model</label>
                  <select id="plane-select" class="w-full p-2 border rounded">
                    <option value="">Select Plane</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium">From Player</label>
                  <input
                    type="text"
                    id="from-player-input"
                    class="w-full p-2 border rounded"
                    required
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium">To Player</label>
                  <input
                    type="text"
                    id="to-player-input"
                    class="w-full p-2 border rounded"
                    required
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium">From Airport</label>
                  <select
                    id="from-airport-select"
                    class="w-full p-2 border rounded"
                  >
                    <option value="">Select From Airport</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium">To Airport</label>
                  <select
                    id="to-airport-select"
                    class="w-full p-2 border rounded"
                  >
                    <option value="">Select To Airport</option>
                  </select>
                </div>
                <div class="flex space-x-3">
                  <button
                    type="submit"
                    class="flex-1 bg-blue-500 text-white py-2 rounded"
                  >
                    Save
                  </button>
                  <button
                    type="button"
                    onclick="hideContractForm()"
                    class="flex-1 bg-gray-500 text-white py-2 rounded"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>

      <!-- Activities Section -->
      <section id="activities" class="section p-4 hidden">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Active Players by Airport</h2>
          <div class="flex gap-2">
            <button
              onclick="showQuickPlayerForm()"
              class="bg-blue-500 text-white px-2 py-1 rounded text-xs"
            >
              ➕ Add Player
            </button>
            <button
              onclick="loadPlayersByAirport()"
              class="bg-green-500 text-white px-2 py-1 rounded text-xs"
            >
              🔄 Refresh
            </button>
          </div>
        </div>

        <!-- Player Activity Summary -->
        <div class="bg-white border rounded-lg p-3 mb-4">
          <h3 class="font-semibold mb-2 text-sm">Quick Stats</h3>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div class="bg-blue-50 p-2 rounded">
              <div class="font-medium" id="unique-players">-</div>
              <div class="text-xs text-gray-600">Unique Players</div>
            </div>
            <div class="bg-green-50 p-2 rounded">
              <div class="font-medium" id="active-today">-</div>
              <div class="text-xs text-gray-600">Active Today</div>
            </div>
          </div>
        </div>

        <div id="activities-list" class="space-y-3">
          <!-- Activities will be loaded here -->
        </div>

        <!-- Activity Form Modal -->
        <div
          id="activity-modal"
          class="fixed inset-0 bg-black bg-opacity-50 hidden"
        >
          <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg p-6 w-full max-w-sm">
              <h3 class="font-semibold mb-4">Add Activity</h3>
              <form id="activity-form" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium">Player ID</label>
                  <input
                    type="text"
                    id="player-input"
                    class="w-full p-2 border rounded"
                    required
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium">Activity Type</label>
                  <select
                    id="activity-type-select"
                    class="w-full p-2 border rounded"
                  >
                    <option value="landing">Landing</option>
                    <option value="takeoff">Takeoff</option>
                    <option value="contract_sent">Contract Sent</option>
                    <option value="contract_received">Contract Received</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="fuel">Fueling</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium">Airport</label>
                  <select
                    id="activity-airport-select"
                    class="w-full p-2 border rounded"
                  >
                    <option value="">Select Airport</option>
                  </select>
                </div>
                <div class="flex space-x-3">
                  <button
                    type="submit"
                    class="flex-1 bg-green-500 text-white py-2 rounded"
                  >
                    Save
                  </button>
                  <button
                    type="button"
                    onclick="hideActivityForm()"
                    class="flex-1 bg-gray-500 text-white py-2 rounded"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>

      <!-- Analytics Section -->
      <section id="analytics" class="section p-4 hidden">
        <h2 class="text-lg font-semibold mb-4">Analytics</h2>

        <div class="space-y-4">
          <button
            onclick="generateInsights()"
            class="w-full bg-purple-500 text-white py-2 rounded"
          >
            🤖 Generate AI Insights
          </button>

          <div
            id="insights-result"
            class="bg-white border rounded-lg p-4 hidden"
          >
            <h3 class="font-semibold mb-2">AI Insights</h3>
            <div id="insights-content" class="text-sm text-gray-700"></div>
          </div>

          <div class="bg-white border rounded-lg p-4">
            <h3 class="font-semibold mb-3">Quick Stats</h3>
            <div id="quick-stats">
              <div class="text-sm text-gray-600">Loading...</div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Voice Recognition Indicator -->
    <div
      id="voice-indicator"
      class="fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded-full text-sm hidden"
    >
      🎤 Listening...
    </div>

    <!-- Quick Player Form Modal -->
    <div
      id="quick-player-modal"
      class="fixed inset-0 bg-black bg-opacity-50 hidden z-50"
    >
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-sm">
          <h3 class="font-semibold mb-4">Quick Add Player Activity</h3>
          <form id="quick-player-form" class="space-y-4">
            <div>
              <label class="block text-sm font-medium">Player ID</label>
              <input
                type="text"
                id="quick-player-input"
                class="w-full p-2 border rounded"
                placeholder="Enter player name/ID"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium">Airport</label>
              <select
                id="quick-player-airport-select"
                class="w-full p-2 border rounded"
                required
              >
                <option value="">Select Airport</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium">Activity Type</label>
              <select
                id="quick-player-activity-select"
                class="w-full p-2 border rounded"
              >
                <option value="landing">Landing</option>
                <option value="takeoff">Takeoff</option>
                <option value="maintenance">Maintenance</option>
                <option value="fuel">Fueling</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div class="flex space-x-3">
              <button
                type="submit"
                class="flex-1 bg-green-500 text-white py-2 rounded"
              >
                Add Player
              </button>
              <button
                type="button"
                onclick="hideQuickPlayerForm()"
                class="flex-1 bg-gray-500 text-white py-2 rounded"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Quick Contract Form Modal -->
    <div
      id="quick-contract-modal"
      class="fixed inset-0 bg-black bg-opacity-50 hidden z-50"
    >
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg p-6 w-full max-w-sm">
          <h3 class="font-semibold mb-4">Quick Add Contract</h3>
          <form id="quick-contract-form" class="space-y-4">
            <div>
              <label class="block text-sm font-medium">From Player (You)</label>
              <input
                type="text"
                id="quick-from-player-input"
                class="w-full p-2 border rounded"
                placeholder="Your player name"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium">To Player</label>
              <input
                type="text"
                id="quick-to-player-input"
                class="w-full p-2 border rounded"
                placeholder="Recipient player name"
                required
              />
            </div>
            <div>
              <label class="block text-sm font-medium">Plane Model</label>
              <select
                id="quick-plane-select"
                class="w-full p-2 border rounded"
                required
              >
                <option value="">Select Plane</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium">From Airport</label>
              <select
                id="quick-from-airport-select"
                class="w-full p-2 border rounded"
                required
              >
                <option value="">Select From Airport</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium">To Airport</label>
              <select
                id="quick-to-airport-select"
                class="w-full p-2 border rounded"
                required
              >
                <option value="">Select To Airport</option>
              </select>
            </div>
            <div class="flex space-x-3">
              <button
                type="submit"
                class="flex-1 bg-blue-500 text-white py-2 rounded"
              >
                Add Contract
              </button>
              <button
                type="button"
                onclick="hideQuickContractForm()"
                class="flex-1 bg-gray-500 text-white py-2 rounded"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script src="/static/app.js"></script>
  </body>
</html>
