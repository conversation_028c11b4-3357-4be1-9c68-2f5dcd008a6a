from pathlib import Path

from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles

from .api import activities, airports, analytics, contracts, planes
from .database import init_database

# Load environment variables
load_dotenv()

app = FastAPI(
    title='World of Airports Tracker',
    description='Track game activities, contracts, and player interactions in World of Airports',
    version='0.1.0',
)

# CORS middleware for frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

# Include API routers
app.include_router(contracts.router, prefix='/api/contracts', tags=['contracts'])
app.include_router(activities.router, prefix='/api/activities', tags=['activities'])
app.include_router(airports.router, prefix='/api/airports', tags=['airports'])
app.include_router(planes.router, prefix='/api/planes', tags=['planes'])
app.include_router(analytics.router, prefix='/api/analytics', tags=['analytics'])

# Mount static files
static_path = Path(__file__).parent.parent / 'static'
if static_path.exists():
    app.mount('/static', StaticFiles(directory=str(static_path)), name='static')


@app.on_event('startup')
async def startup_event():
    """Initialize database on startup"""
    await init_database()


@app.get('/', response_class=HTMLResponse)
async def root():
    """Serve the main application interface"""
    try:
        static_path = Path(__file__).parent.parent / 'static' / 'index.html'
        if static_path.exists():
            with open(static_path, 'r') as f:
                return HTMLResponse(content=f.read())
    except Exception:
        pass

    # Fallback minimal interface
    return HTMLResponse(
        content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>World of Airports Tracker</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-50 min-h-screen">
        <div class="max-w-md mx-auto p-4">
            <h1 class="text-2xl font-bold text-center mb-8">WoA Tracker</h1>
            <div class="text-center">
                <p class="mb-4">World of Airports Game Tracker</p>
                <a href="/docs" class="bg-blue-500 text-white px-4 py-2 rounded">API Documentation</a>
            </div>
        </div>
    </body>
    </html>
    """
    )


@app.get('/health')
async def health_check():
    """Health check endpoint for monitoring"""
    return {'status': 'healthy', 'service': 'woa-tracker'}


if __name__ == '__main__':
    import os

    import uvicorn

    host = os.getenv('FASTAPI_HOST', '0.0.0.0')
    port = int(os.getenv('FASTAPI_PORT', '8000'))
    reload = os.getenv('FASTAPI_RELOAD', 'false').lower() == 'true'

    uvicorn.run(app, host=host, port=port, reload=reload)
