from datetime import datetime

import aiosqlite
from fastapi import APIRouter, Depends, HTTPException

from ..database import get_dashboard_stats, get_db
from ..llm import generate_insights, process_natural_language_query
from ..models import DashboardStats, NaturalQueryRequest, NaturalQueryResponse

router = APIRouter()


@router.get('/dashboard', response_model=DashboardStats)
async def get_dashboard(db: aiosqlite.Connection = Depends(get_db)):
    """Get dashboard statistics"""
    stats = await get_dashboard_stats()

    # Get recent activities with details
    db.row_factory = aiosqlite.Row
    cursor = await db.execute("""
        SELECT
            pa.id, pa.last_active, pa.player_id, pa.activity_type,
            a.id as airport_id, a.code, a.name, a.region
        FROM player_activities pa
        JOIN airports a ON pa.airport_id = a.id
        ORDER BY pa.last_active DESC
        LIMIT 10
    """)
    rows = await cursor.fetchall()

    recent_activities = []
    for row in rows:
        recent_activities.append({
            'id': row['id'],
            'last_active': row['last_active'],
            'player_id': row['player_id'],
            'activity_type': row['activity_type'],
            'airport': {'id': row['airport_id'], 'code': row['code'], 'name': row['name'], 'region': row['region']},
        })

    stats['recent_activities'] = recent_activities
    return stats


@router.post('/query', response_model=NaturalQueryResponse)
async def natural_language_query(request: NaturalQueryRequest):
    """Process natural language queries about game data"""
    try:
        response = await process_natural_language_query(request.query)
        return response
    except Exception as e:
        raise HTTPException(status_code=400, detail=f'Query processing failed: {str(e)}')


@router.get('/insights')
async def get_game_insights():
    """Get AI-generated insights about game patterns"""
    try:
        insights = await generate_insights()
        return {'insights': insights, 'timestamp': datetime.now()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'Failed to generate insights: {str(e)}')


@router.get('/stats/contracts')
async def get_contract_stats(db: aiosqlite.Connection = Depends(get_db)):
    """Get detailed contract statistics"""
    db.row_factory = aiosqlite.Row

    # Contracts by status
    cursor = await db.execute("""
        SELECT status, COUNT(*) as count
        FROM contracts
        GROUP BY status
    """)
    by_status = [dict(row) for row in await cursor.fetchall()]

    # Contracts by plane level
    cursor = await db.execute("""
        SELECT pm.level, COUNT(*) as count
        FROM contracts c
        JOIN plane_models pm ON c.plane_model_id = pm.id
        GROUP BY pm.level
        ORDER BY pm.level
    """)
    by_plane_level = [dict(row) for row in await cursor.fetchall()]

    # Contracts by airport
    cursor = await db.execute("""
        SELECT a.code, a.name, COUNT(*) as count
        FROM contracts c
        JOIN airports a ON c.airport_id = a.id
        GROUP BY a.id, a.code, a.name
        ORDER BY count DESC
        LIMIT 10
    """)
    by_airport = [dict(row) for row in await cursor.fetchall()]

    # Daily contract trends (last 30 days)
    cursor = await db.execute("""
        SELECT
            date(contract_date) as date,
            COUNT(*) as count
        FROM contracts
        WHERE contract_date >= date('now', '-30 days')
        GROUP BY date(contract_date)
        ORDER BY date
    """)
    daily_trends = [dict(row) for row in await cursor.fetchall()]

    return {
        'by_status': by_status,
        'by_plane_level': by_plane_level,
        'by_airport': by_airport,
        'daily_trends': daily_trends,
    }


@router.get('/stats/activities')
async def get_activity_stats(db: aiosqlite.Connection = Depends(get_db)):
    """Get detailed activity statistics"""
    db.row_factory = aiosqlite.Row

    # Activities by type
    cursor = await db.execute("""
        SELECT activity_type, COUNT(*) as count
        FROM player_activities
        GROUP BY activity_type
        ORDER BY count DESC
    """)
    by_type = [dict(row) for row in await cursor.fetchall()]

    # Activities by airport
    cursor = await db.execute("""
        SELECT a.code, a.name, COUNT(*) as count
        FROM player_activities pa
        JOIN airports a ON pa.airport_id = a.id
        GROUP BY a.id, a.code, a.name
        ORDER BY count DESC
        LIMIT 10
    """)
    by_airport = [dict(row) for row in await cursor.fetchall()]

    # Player activity summary
    cursor = await db.execute("""
        SELECT
            player_id,
            COUNT(*) as total_activities,
            COUNT(DISTINCT activity_type) as unique_activities,
            MAX(last_active) as last_seen
        FROM player_activities
        GROUP BY player_id
        ORDER BY total_activities DESC
        LIMIT 20
    """)
    player_summary = [dict(row) for row in await cursor.fetchall()]

    # Hourly activity patterns
    cursor = await db.execute("""
        SELECT
            strftime('%H', last_active) as hour,
            COUNT(*) as count
        FROM player_activities
        WHERE last_active >= date('now', '-7 days')
        GROUP BY strftime('%H', last_active)
        ORDER BY hour
    """)
    hourly_patterns = [dict(row) for row in await cursor.fetchall()]

    return {
        'by_type': by_type,
        'by_airport': by_airport,
        'player_summary': player_summary,
        'hourly_patterns': hourly_patterns,
    }


@router.get('/export/csv')
async def export_data_csv(table: str = 'contracts', days: int = 30, db: aiosqlite.Connection = Depends(get_db)):
    """Export data as CSV"""
    if table not in ['contracts', 'activities']:
        raise HTTPException(status_code=400, detail="Invalid table. Use 'contracts' or 'activities'")

    db.row_factory = aiosqlite.Row

    if table == 'contracts':
        cursor = await db.execute(
            """
            SELECT
                c.id, c.contract_date, c.status, c.recipient_player_id,
                pm.model_name, pm.level, a.code as airport_code, a.name as airport_name
            FROM contracts c
            JOIN plane_models pm ON c.plane_model_id = pm.id
            JOIN airports a ON c.airport_id = a.id
            WHERE c.contract_date >= date('now', '-{} days')
            ORDER BY c.contract_date DESC
        """.format(days)
        )
    else:  # activities
        cursor = await db.execute(
            """
            SELECT
                pa.id, pa.last_active, pa.player_id, pa.activity_type,
                a.code as airport_code, a.name as airport_name
            FROM player_activities pa
            JOIN airports a ON pa.airport_id = a.id
            WHERE pa.last_active >= date('now', '-{} days')
            ORDER BY pa.last_active DESC
        """.format(days)
        )

    rows = await cursor.fetchall()

    if not rows:
        return {'message': 'No data found for the specified period'}

    # Convert to CSV format
    headers = list(rows[0].keys())
    csv_content = ','.join(headers) + '\n'

    for row in rows:
        csv_content += ','.join(str(row[col]) for col in headers) + '\n'

    return {
        'filename': f'{table}_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv',
        'content': csv_content,
        'row_count': len(rows),
    }
