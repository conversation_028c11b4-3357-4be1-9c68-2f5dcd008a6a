[project]
name = "woa-tracker"
version = "0.1.0"
description = "World of Airports game activity tracker with LLM-powered analytics"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi==0.110.0",
    "uvicorn[standard]==0.27.0",
    "aiosqlite==0.19.0",
    "litellm==1.35.0",
    "pydantic==2.6.0",
    "python-jose[cryptography]==3.3.0",
    "python-multipart==0.0.9",
    "aiofiles==23.2.1",
    "passlib[bcrypt]==1.7.4",
    "pyjwt>=2.10.1",
    "python-dotenv==1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]
