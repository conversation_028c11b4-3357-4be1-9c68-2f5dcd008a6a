
services:
  web:
    build: .
    ports:
      - "${FASTAPI_PORT:-8000}:8000"
    env_file:
      - .env
    environment:
      # Override specific values for container environment
      - DATABASE_PATH=/app/data/game_tracker.db
      - FASTAPI_HOST=0.0.0.0
      - FASTAPI_PORT=8000
    volumes:
      - ./data:/app/data
      - ./backups:/app/backups
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Optional: Reverse proxy with SSL (uncomment for production)
  # traefik:
  #   image: traefik:v3.0
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock:ro
  #     - ./traefik:/etc/traefik:ro
  #     - ./acme.json:/acme.json
  #   restart: unless-stopped
  #   labels:
  #     - "traefik.enable=true"

  # Optional: Database backup service (uncomment if needed)
  # backup:
  #   image: alpine:latest
  #   volumes:
  #     - ./data:/data:ro
  #     - ./backups:/backups
  #   environment:
  #     - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
  #   command: |
  #     sh -c "
  #     echo '#!/bin/sh' > /backup.sh &&
  #     echo 'DATE=\$$(date +%Y%m%d_%H%M%S)' >> /backup.sh &&
  #     echo 'cp /data/game_tracker.db /backups/game_tracker_\$$DATE.db' >> /backup.sh &&
  #     echo 'find /backups -name \"*.db\" -mtime +30 -delete' >> /backup.sh &&
  #     chmod +x /backup.sh &&
  #     crond -f
  #     "
  #   restart: unless-stopped

volumes:
  data:
    driver: local
  backups:
    driver: local