# World of Airports Tracker

A comprehensive game tracking application for World of Airports with LLM-powered analytics and mobile-first design.

## Features

- **Contract Tracking**: Track plane contracts between players
- **Activity Monitoring**: Monitor player activities across airports
- **Natural Language Queries**: Ask questions about your data using AI
- **Voice Input**: Use voice commands for quick data entry
- **Mobile-First Design**: Optimized for mobile devices with PWA support
- **Real-time Analytics**: AI-powered insights and patterns analysis
- **RESTful API**: Complete API for integration and automation

## Quick Start

### Using Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd WoA_tracker
   ```

2. **Set up environment variables**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env and add your LLM API key
   OPENAI_API_KEY=your_openai_key_here
   # OR
   ANTHROPIC_API_KEY=your_anthropic_key_here
   ```

3. **Start the application**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Web Interface: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Development Setup

1. **Install Python dependencies**
   ```bash
   pip install -e .
   ```

2. **Run the development server**
   ```bash
   uvicorn src.main:app --reload
   ```

## Configuration

### Environment Variables

- `DATABASE_PATH`: Path to SQLite database file (default: `data/game_tracker.db`)
- `JWT_SECRET_KEY`: Secret key for JWT tokens (change in production!)
- `OPENAI_API_KEY`: OpenAI API key for LLM features
- `ANTHROPIC_API_KEY`: Anthropic API key for LLM features (alternative to OpenAI)

### LLM Configuration

The application supports multiple LLM providers:
- **OpenAI GPT-4o mini**: Recommended for cost-effectiveness
- **Anthropic Claude Haiku**: Alternative option
- **Local LLM**: Extend `src/llm.py` for local model support

## API Endpoints

### Core Data
- `GET /api/airports/` - List all airports
- `GET /api/planes/` - List all plane models
- `POST /api/contracts/` - Create contract
- `GET /api/contracts/` - List contracts
- `POST /api/activities/` - Create activity
- `GET /api/activities/` - List activities

### Analytics
- `GET /api/analytics/dashboard` - Dashboard statistics
- `POST /api/analytics/query` - Natural language queries
- `GET /api/analytics/insights` - AI-generated insights
- `GET /api/analytics/stats/contracts` - Contract statistics
- `GET /api/analytics/stats/activities` - Activity statistics

## Game Data

### Airports
The system includes 14 World of Airports game airports:
- INN (Innsbruck)
- BRI (Brisbane)
- IAD (Washington Dulles)
- SAN (San Diego)
- LHR (London Heathrow)
- PRG (Prague)
- PHX (Phoenix)
- NRT (Tokyo Narita)
- MCT (Muscat)
- KWI (Kuwait)
- NGO (Nagoya)
- LGA (New York LaGuardia)
- SXM (Sint Maarten)
- BKK (Bangkok)

### Plane Models
Supports all plane levels A-G with common aircraft:
- **Level A**: A380, Boeing 747
- **Level B**: Boeing 777, Airbus A350
- **Level C**: Boeing 787, Airbus A330
- **Level D**: Boeing 767, Airbus A321
- **Level E**: Boeing 737, Airbus A320
- **Level F**: Embraer E-Jet, Bombardier CRJ
- **Level G**: ATR 72, Dash 8

## Usage Examples

### Natural Language Queries
- "Show A380 contracts from last week"
- "Which airports have the most activity?"
- "Who are the most active players?"
- "What are the most popular plane types?"

### Voice Commands
- "Add contract A380 to John at Innsbruck"
- "Show recent activities"
- "Generate insights"

## Development

### Project Structure
```
WoA_tracker/
   src/
      main.py              # FastAPI application
      models.py            # Pydantic models
      database.py          # Database operations
      llm.py               # LLM integration
      auth.py              # Authentication
      api/                 # API endpoints
   static/                  # Frontend files
   data/                    # Database storage
   docker-compose.yml       # Docker setup
   Dockerfile              # Container definition
```

### Adding New Features

1. **New API Endpoints**: Add to `src/api/`
2. **Database Schema**: Update `src/database.py`
3. **LLM Integration**: Extend `src/llm.py`
4. **Frontend**: Modify `static/index.html` and `static/app.js`

## Deployment

### Production Deployment

1. **Configure environment variables**
2. **Set up reverse proxy** (uncomment Traefik in docker-compose.yml)
3. **Enable backups** (uncomment backup service)
4. **Configure SSL certificates**
5. **Set up monitoring**

### NAS Deployment

The application is optimized for NAS deployment:
- Minimal resource usage
- SQLite database (no external database required)
- Docker Compose for easy management
- Built-in backup capabilities

## Troubleshooting

### Common Issues

1. **LLM API not working**
   - Check API key configuration
   - Verify internet connectivity
   - Check API usage limits

2. **Database errors**
   - Ensure data directory exists
   - Check file permissions
   - Verify SQLite installation

3. **Voice recognition not working**
   - Use HTTPS for voice features
   - Check browser permissions
   - Verify microphone access

### Logs and Monitoring

- Application logs: `docker-compose logs web`
- Health check: `curl http://localhost:8000/health`
- Database status: Check `data/game_tracker.db`

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review API documentation at `/docs`
3. Check database logs and status
4. Verify LLM API configuration