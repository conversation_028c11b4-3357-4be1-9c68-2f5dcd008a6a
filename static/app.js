// Global app state
let airports = [];
let planeModels = [];
let recognition = null;

// Initialize app
document.addEventListener("DOMContentLoaded", function () {
  initApp();
});

async function initApp() {
  try {
    // Load reference data
    await loadAirports();
    await loadPlaneModels();

    // Load dashboard data
    await loadDashboard();

    // Setup voice recognition
    setupVoiceRecognition();

    console.log("App initialized successfully");
  } catch (error) {
    console.error("Failed to initialize app:", error);
    showError("Failed to initialize app");
  }
}

// API Helper functions
async function apiCall(endpoint, options = {}) {
  const baseUrl = window.location.origin;
  const response = await fetch(`${baseUrl}${endpoint}`, {
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }

  return await response.json();
}

// Navigation
function showSection(sectionId) {
  // Hide all sections
  document.querySelectorAll(".section").forEach((section) => {
    section.classList.add("hidden");
  });

  // Remove active class from all nav buttons
  document.querySelectorAll(".nav-btn").forEach((btn) => {
    btn.classList.remove("active", "bg-blue-700");
  });

  // Show selected section
  document.getElementById(sectionId).classList.remove("hidden");

  // Add active class to clicked button
  event.target.classList.add("active", "bg-blue-700");

  // Load section-specific data
  switch (sectionId) {
    case "contracts":
      loadContractsSection();
      break;
    case "activities":
      loadPlayersByAirport();
      break;
    case "analytics":
      loadAnalytics();
      break;
  }
}

// Load reference data
async function loadAirports() {
  try {
    airports = await apiCall("/api/airports/");

    // Populate airport selects
    const selects = [
      "from-airport-select",
      "to-airport-select",
      "activity-airport-select",
      "quick-player-airport-select",
      "quick-from-airport-select",
      "quick-to-airport-select",
    ];
    selects.forEach((selectId) => {
      const select = document.getElementById(selectId);
      if (select) {
        const placeholder = selectId.includes("from")
          ? "Select From Airport"
          : selectId.includes("to")
          ? "Select To Airport"
          : "Select Airport";
        select.innerHTML = `<option value="">${placeholder}</option>`;
        airports.forEach((airport) => {
          select.innerHTML += `<option value="${airport.id}">${airport.code} - ${airport.name}</option>`;
        });
      }
    });
  } catch (error) {
    console.error("Failed to load airports:", error);
  }
}

async function loadPlaneModels() {
  try {
    planeModels = await apiCall("/api/planes/");

    // Populate plane model selects
    const selects = ["plane-select", "quick-plane-select"];
    selects.forEach((selectId) => {
      const select = document.getElementById(selectId);
      if (select) {
        select.innerHTML = '<option value="">Select Plane</option>';
        planeModels.forEach((plane) => {
          select.innerHTML += `<option value="${plane.id}">${plane.model_name} (Level ${plane.level})</option>`;
        });
      }
    });
  } catch (error) {
    console.error("Failed to load plane models:", error);
  }
}

// Dashboard functions
async function loadDashboard() {
  try {
    const [stats, activities, contractStats] = await Promise.all([
      apiCall("/api/analytics/dashboard"),
      apiCall("/api/activities/?limit=10"),
      apiCall("/api/analytics/stats/contracts"),
    ]);

    // Update key metrics
    const pendingContracts =
      contractStats.by_status.find((s) => s.status === "pending")?.count || 0;
    document.getElementById("pending-contracts").textContent = pendingContracts;
    document.getElementById("active-players").textContent =
      stats.active_players_today || 0;
    document.getElementById("total-activities").textContent = activities.length;

    // Load airport activity
    await loadAirportActivity();

    // Load pending contracts summary
    await loadPendingContracts();

    // Load recent player activity
    loadRecentActivity(stats.recent_activities || activities.slice(0, 5));
  } catch (error) {
    console.error("Failed to load dashboard:", error);
    showError("Failed to load dashboard data");
  }
}

// New airport activity tracking
async function loadAirportActivity() {
  try {
    const activityStats = await apiCall("/api/analytics/stats/activities");
    const container = document.getElementById("airport-activity-list");

    if (!activityStats.by_airport || activityStats.by_airport.length === 0) {
      container.innerHTML =
        '<div class="text-gray-500 text-sm">No airport activity data</div>';
      return;
    }

    const busyAirports = activityStats.by_airport.slice(0, 3);
    document.getElementById("busy-airports").textContent = busyAirports.length;

    container.innerHTML = busyAirports
      .map(
        (airport) => `
            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                <div>
                    <div class="font-medium text-sm">${airport.code} - ${airport.name}</div>
                    <div class="text-xs text-gray-600">${airport.count} activities</div>
                </div>
                <div class="text-right">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                        ${airport.count}
                    </div>
                </div>
            </div>
        `
      )
      .join("");
  } catch (error) {
    console.error("Failed to load airport activity:", error);
  }
}

// Load pending contracts for dashboard
async function loadPendingContracts() {
  try {
    const contracts = await apiCall("/api/contracts/?limit=5");
    const container = document.getElementById("pending-contracts-list");

    const pendingContracts = contracts.filter(
      (c) => c.acceptance_status === "pending"
    );

    if (pendingContracts.length === 0) {
      container.innerHTML =
        '<div class="text-gray-500 text-sm">No pending contracts</div>';
      return;
    }

    container.innerHTML = pendingContracts
      .slice(0, 3)
      .map(
        (contract) => `
            <div class="flex justify-between items-center p-2 bg-yellow-50 rounded">
                <div>
                    <div class="font-medium text-sm">${
                      contract.plane_model.model_name
                    }</div>
                    <div class="text-xs text-gray-600">${
                      contract.from_player
                    } → ${contract.to_player}</div>
                    <div class="text-xs text-gray-500">${
                      contract.from_airport.code
                    } → ${contract.to_airport.code}</div>
                </div>
                <div class="text-right">
                    <div class="text-xs text-yellow-600 font-medium">⏳ ${formatTimeAgo(
                      contract.contract_date
                    )}</div>
                </div>
            </div>
        `
      )
      .join("");
  } catch (error) {
    console.error("Failed to load pending contracts:", error);
  }
}

// Load recent player activity for dashboard
function loadRecentActivity(activities) {
  const container = document.getElementById("recent-activity-list");

  if (!activities || activities.length === 0) {
    container.innerHTML =
      '<div class="text-gray-500 text-sm">No recent activity</div>';
    return;
  }

  container.innerHTML = activities
    .map(
      (activity) => `
        <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
            <div>
                <div class="font-medium text-sm">${activity.player_id}</div>
                <div class="text-xs text-gray-600">${
                  activity.activity_type
                } at ${activity.airport.code}</div>
            </div>
            <div class="text-xs text-gray-500">${formatTimeAgo(
              activity.last_active
            )}</div>
        </div>
    `
    )
    .join("");
}

// Enhanced contracts section
async function loadContractsSection() {
  try {
    const [contracts, contractStats] = await Promise.all([
      apiCall("/api/contracts/?limit=20"),
      apiCall("/api/analytics/stats/contracts"),
    ]);

    // Update contract counts
    const pendingCount =
      contractStats.by_status.find((s) => s.status === "pending")?.count || 0;
    const activeCount =
      contractStats.by_status.find((s) => s.status === "active")?.count || 0;
    const completedCount =
      contractStats.by_status.find((s) => s.status === "completed")?.count || 0;

    document.getElementById("pending-count").textContent = pendingCount;
    document.getElementById("accepted-count").textContent = activeCount;
    document.getElementById("rejected-count").textContent = completedCount;

    // Load contracts list
    loadContractsList(contracts);
  } catch (error) {
    console.error("Failed to load contracts section:", error);
    showError("Failed to load contracts");
  }
}

function loadContractsList(contracts) {
  const container = document.getElementById("contracts-list");

  if (contracts.length === 0) {
    container.innerHTML =
      '<div class="text-gray-500 text-center py-4">No contracts found</div>';
    return;
  }

  container.innerHTML = contracts
    .map(
      (contract) => `
        <div class="bg-white border rounded-lg p-3">
            <div class="flex justify-between items-start">
                <div>
                    <div class="font-medium">${
                      contract.plane_model.model_name
                    }</div>
                    <div class="text-sm text-gray-600">From: ${
                      contract.from_player
                    } → To: ${contract.to_player}</div>
                    <div class="text-sm text-gray-600">${
                      contract.from_airport.code
                    } → ${contract.to_airport.code}</div>
                    <div class="text-xs text-gray-500">${formatDate(
                      contract.contract_date
                    )}</div>
                </div>
                <div class="flex flex-col items-end">
                    <span class="px-2 py-1 rounded text-xs ${getStatusColor(
                      contract.status
                    )}">${contract.status}</span>
                    <span class="px-2 py-1 rounded text-xs ${getAcceptanceStatusColor(
                      contract.acceptance_status
                    )} mt-1">${contract.acceptance_status}</span>
                    <div class="text-xs text-gray-500 mt-1">Level ${
                      contract.plane_model.level
                    }</div>
                </div>
            </div>
        </div>
    `
    )
    .join("");
}

// Contract filtering
let currentContractFilter = "all";
async function filterContracts(filter) {
  currentContractFilter = filter;

  // Update button states
  document
    .getElementById("pending-filter")
    .classList.toggle("bg-yellow-500", filter === "pending");
  document
    .getElementById("pending-filter")
    .classList.toggle("bg-gray-400", filter !== "pending");
  document
    .getElementById("all-filter")
    .classList.toggle("bg-gray-500", filter === "all");
  document
    .getElementById("all-filter")
    .classList.toggle("bg-gray-400", filter !== "all");

  try {
    let contracts;
    if (filter === "pending") {
      contracts = await apiCall("/api/contracts/?limit=20");
      contracts = contracts.filter((c) => c.acceptance_status === "pending");
    } else {
      contracts = await apiCall("/api/contracts/?limit=20");
    }

    loadContractsList(contracts);
  } catch (error) {
    console.error("Failed to filter contracts:", error);
  }
}

function showContractForm() {
  document.getElementById("contract-modal").classList.remove("hidden");
}

function hideContractForm() {
  document.getElementById("contract-modal").classList.add("hidden");
  document.getElementById("contract-form").reset();
}

// Alias for backward compatibility
function loadContracts() {
  loadContractsSection();
}

// Alias for backward compatibility
function loadActivities() {
  loadPlayersByAirport();
}

// Handle contract form submission
document
  .getElementById("contract-form")
  .addEventListener("submit", async function (e) {
    e.preventDefault();

    const contractData = {
      plane_model_id: parseInt(document.getElementById("plane-select").value),
      from_player: document.getElementById("from-player-input").value,
      to_player: document.getElementById("to-player-input").value,
      from_airport: parseInt(
        document.getElementById("from-airport-select").value
      ),
      to_airport: parseInt(document.getElementById("to-airport-select").value),
    };

    try {
      await apiCall("/api/contracts/", {
        method: "POST",
        body: JSON.stringify(contractData),
      });

      hideContractForm();
      loadContracts();
      loadDashboard();
      showSuccess("Contract added successfully!");
    } catch (error) {
      console.error("Failed to create contract:", error);
      showError("Failed to create contract");
    }
  });

// Enhanced activities - players by airport
async function loadPlayersByAirport() {
  try {
    const [activities, activityStats, players] = await Promise.all([
      apiCall("/api/activities/?limit=50"),
      apiCall("/api/analytics/stats/activities"),
      apiCall("/api/activities/players"),
    ]);

    // Update stats
    document.getElementById("unique-players").textContent = players.length;
    const todayActivePlayers = players.filter((p) =>
      isToday(p.last_seen)
    ).length;
    document.getElementById("active-today").textContent = todayActivePlayers;

    // Group activities by airport
    const airportGroups = {};
    activities.forEach((activity) => {
      const airportKey = activity.airport.code;
      if (!airportGroups[airportKey]) {
        airportGroups[airportKey] = {
          airport: activity.airport,
          players: new Set(),
          activities: [],
        };
      }
      airportGroups[airportKey].players.add(activity.player_id);
      airportGroups[airportKey].activities.push(activity);
    });

    const container = document.getElementById("activities-list");

    if (Object.keys(airportGroups).length === 0) {
      container.innerHTML =
        '<div class="text-gray-500 text-center py-4">No airport activity found</div>';
      return;
    }

    // Sort by number of active players
    const sortedAirports = Object.values(airportGroups).sort(
      (a, b) => b.players.size - a.players.size
    );

    container.innerHTML = sortedAirports
      .map(
        (group) => `
            <div class="bg-white border rounded-lg p-3">
                <div class="flex justify-between items-start mb-2">
                    <div>
                        <div class="font-medium">${group.airport.code} - ${
          group.airport.name
        }</div>
                        <div class="text-sm text-gray-600">${
                          group.airport.region
                        }</div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium text-blue-600">${
                          group.players.size
                        } players</div>
                        <div class="text-xs text-gray-500">${
                          group.activities.length
                        } activities</div>
                    </div>
                </div>
                <div class="border-t pt-2">
                    <div class="text-xs text-gray-600 mb-1">Recent players:</div>
                    <div class="flex flex-wrap gap-1">
                        ${Array.from(group.players)
                          .slice(0, 5)
                          .map(
                            (player) =>
                              `<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">${player}</span>`
                          )
                          .join("")}
                        ${
                          group.players.size > 5
                            ? `<span class="text-xs text-gray-500">+${
                                group.players.size - 5
                              } more</span>`
                            : ""
                        }
                    </div>
                </div>
            </div>
        `
      )
      .join("");
  } catch (error) {
    console.error("Failed to load players by airport:", error);
    showError("Failed to load player activities");
  }
}

function showActivityForm() {
  document.getElementById("activity-modal").classList.remove("hidden");
}

function hideActivityForm() {
  document.getElementById("activity-modal").classList.add("hidden");
  document.getElementById("activity-form").reset();
}

// Handle activity form submission
document
  .getElementById("activity-form")
  .addEventListener("submit", async function (e) {
    e.preventDefault();

    const activityData = {
      player_id: document.getElementById("player-input").value,
      activity_type: document.getElementById("activity-type-select").value,
      airport_id: parseInt(
        document.getElementById("activity-airport-select").value
      ),
    };

    try {
      await apiCall("/api/activities/", {
        method: "POST",
        body: JSON.stringify(activityData),
      });

      hideActivityForm();
      loadActivities();
      loadDashboard();
      showSuccess("Activity added successfully!");
    } catch (error) {
      console.error("Failed to create activity:", error);
      showError("Failed to create activity");
    }
  });

// Handle quick player form submission
document
  .getElementById("quick-player-form")
  .addEventListener("submit", async function (e) {
    e.preventDefault();

    const activityData = {
      player_id: document.getElementById("quick-player-input").value,
      activity_type: document.getElementById("quick-player-activity-select")
        .value,
      airport_id: parseInt(
        document.getElementById("quick-player-airport-select").value
      ),
    };

    try {
      await apiCall("/api/activities/", {
        method: "POST",
        body: JSON.stringify(activityData),
      });

      hideQuickPlayerForm();
      loadActivities();
      loadDashboard();
      showSuccess("Player activity added successfully!");
    } catch (error) {
      console.error("Failed to create player activity:", error);
      showError("Failed to create player activity");
    }
  });

// Handle quick contract form submission
document
  .getElementById("quick-contract-form")
  .addEventListener("submit", async function (e) {
    e.preventDefault();

    const contractData = {
      plane_model_id: parseInt(
        document.getElementById("quick-plane-select").value
      ),
      from_player: document.getElementById("quick-from-player-input").value,
      to_player: document.getElementById("quick-to-player-input").value,
      from_airport: parseInt(
        document.getElementById("quick-from-airport-select").value
      ),
      to_airport: parseInt(
        document.getElementById("quick-to-airport-select").value
      ),
    };

    try {
      await apiCall("/api/contracts/", {
        method: "POST",
        body: JSON.stringify(contractData),
      });

      hideQuickContractForm();
      loadContracts();
      loadDashboard();
      showSuccess("Contract added successfully!");
    } catch (error) {
      console.error("Failed to create contract:", error);
      showError("Failed to create contract");
    }
  });

// Analytics functions
async function loadAnalytics() {
  try {
    const contractStats = await apiCall("/api/analytics/stats/contracts");
    const activityStats = await apiCall("/api/analytics/stats/activities");

    const quickStats = document.getElementById("quick-stats");
    quickStats.innerHTML = `
            <div class="mb-3">
                <h4 class="font-medium mb-2">Top Airports by Contracts</h4>
                ${contractStats.by_airport
                  .slice(0, 5)
                  .map(
                    (item) =>
                      `<div class="flex justify-between text-sm">
                        <span>${item.code}</span>
                        <span>${item.count}</span>
                    </div>`
                  )
                  .join("")}
            </div>
            <div>
                <h4 class="font-medium mb-2">Activity Types</h4>
                ${activityStats.by_type
                  .slice(0, 5)
                  .map(
                    (item) =>
                      `<div class="flex justify-between text-sm">
                        <span>${item.activity_type}</span>
                        <span>${item.count}</span>
                    </div>`
                  )
                  .join("")}
            </div>
        `;
  } catch (error) {
    console.error("Failed to load analytics:", error);
    showError("Failed to load analytics");
  }
}

async function generateInsights() {
  try {
    const button = event.target;
    button.disabled = true;
    button.textContent = "🤖 Generating...";

    const insights = await apiCall("/api/analytics/insights");

    document.getElementById("insights-result").classList.remove("hidden");
    document.getElementById("insights-content").textContent = insights.insights;

    button.disabled = false;
    button.textContent = "🤖 Generate AI Insights";
  } catch (error) {
    console.error("Failed to generate insights:", error);
    showError("Failed to generate insights. Make sure LLM API is configured.");

    const button = event.target;
    button.disabled = false;
    button.textContent = "🤖 Generate AI Insights";
  }
}

// Natural Language Query
async function processQuery() {
  const query = document.getElementById("nl-query").value.trim();
  if (!query) return;

  try {
    const button = event.target;
    button.disabled = true;
    button.textContent = "Processing...";

    const result = await apiCall("/api/analytics/query", {
      method: "POST",
      body: JSON.stringify({ query: query }),
    });

    document.getElementById("query-result").classList.remove("hidden");
    document.getElementById("query-interpretation").textContent =
      result.interpretation;

    button.disabled = false;
    button.textContent = "Ask AI";
  } catch (error) {
    console.error("Failed to process query:", error);
    showError("Failed to process query. Make sure LLM API is configured.");

    const button = event.target;
    button.disabled = false;
    button.textContent = "Ask AI";
  }
}

// Voice Recognition
function setupVoiceRecognition() {
  if ("webkitSpeechRecognition" in window || "SpeechRecognition" in window) {
    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    recognition = new SpeechRecognition();
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = "en-US";

    recognition.onstart = function () {
      document.getElementById("voice-indicator").classList.remove("hidden");
    };

    recognition.onend = function () {
      document.getElementById("voice-indicator").classList.add("hidden");
    };

    recognition.onresult = function (event) {
      const transcript = event.results[0][0].transcript;
      document.getElementById("nl-query").value = transcript;
    };

    recognition.onerror = function (event) {
      console.error("Speech recognition error:", event.error);
      showError("Speech recognition failed");
    };
  }
}

function startVoiceQuery() {
  if (recognition) {
    recognition.start();
  } else {
    showError("Speech recognition not supported in this browser");
  }
}

// Quick Entry functions
function showQuickEntry(type) {
  if (type === "contract") {
    showContractForm();
  } else if (type === "activity") {
    showActivityForm();
  }
}

// Utility functions
function formatDate(dateString) {
  const date = new Date(dateString);
  return (
    date.toLocaleDateString() +
    " " +
    date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  );
}

function formatTimeAgo(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return "now";
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  return `${diffDays}d ago`;
}

function isToday(dateString) {
  const date = new Date(dateString);
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

function getStatusColor(status) {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800";
    case "completed":
      return "bg-blue-100 text-blue-800";
    case "cancelled":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

function getAcceptanceStatusColor(status) {
  switch (status) {
    case "pending":
      return "bg-yellow-100 text-yellow-800";
    case "accepted":
      return "bg-green-100 text-green-800";
    case "rejected":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

function showError(message) {
  // Simple error notification - could be enhanced with a toast library
  alert("Error: " + message);
}

function showSuccess(message) {
  // Simple success notification - could be enhanced with a toast library
  console.log("Success: " + message);
}

// Quick Player Form Functions
function showQuickPlayerForm() {
  document.getElementById("quick-player-modal").classList.remove("hidden");
}

function hideQuickPlayerForm() {
  document.getElementById("quick-player-modal").classList.add("hidden");
  document.getElementById("quick-player-form").reset();
}

// Quick Contract Form Functions
function showQuickContractForm() {
  document.getElementById("quick-contract-modal").classList.remove("hidden");
}

function hideQuickContractForm() {
  document.getElementById("quick-contract-modal").classList.add("hidden");
  document.getElementById("quick-contract-form").reset();
}
