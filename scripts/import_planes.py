#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to import plane models from CSV file into the database.
This script will replace all existing data in the plane_models table.
"""

import asyncio
import csv
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

import aiosqlite

from database import DATABASE_PATH


async def import_planes_from_csv():
    """Import plane models from CSV file, replacing all existing data."""

    # Path to the CSV file
    csv_path = Path(__file__).parent.parent / 'data' / 'planes.csv'

    if not csv_path.exists():
        print(f'Error: CSV file not found at {csv_path}')
        return False

    print(f'Reading CSV file: {csv_path}')

    # Read CSV data
    planes_data = []
    try:
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                planes_data.append({
                    'model_name': row['Plane_Model'].strip(),
                    'level': row['Level'].strip().upper(),
                    'type': row['Type'].strip().upper(),
                })

        print(f'Successfully read {len(planes_data)} plane models from CSV')

    except Exception as e:
        print(f'Error reading CSV file: {e}')
        return False

    # Validate data
    valid_levels = {'A', 'B', 'C', 'D', 'E', 'F', 'G'}
    valid_types = {'PAX', 'CARGO'}

    for i, plane in enumerate(planes_data):
        if plane['level'] not in valid_levels:
            print(f"Error: Invalid level '{plane['level']}' for plane {plane['model_name']} at row {i + 2}")
            return False

        if plane['type'] not in valid_types:
            print(f"Error: Invalid type '{plane['type']}' for plane {plane['model_name']} at row {i + 2}")
            return False

    print('Data validation passed')

    # Connect to database and import data
    try:
        # Ensure data directory exists
        Path(DATABASE_PATH).parent.mkdir(parents=True, exist_ok=True)

        async with aiosqlite.connect(DATABASE_PATH) as db:
            print('Connected to database')

            # Check if there are any contracts that reference plane models
            cursor = await db.execute('SELECT COUNT(*) FROM contracts')
            result = await cursor.fetchone()
            contract_count = result[0] if result else 0

            if contract_count > 0:
                print(
                    f'Warning: Found {contract_count} existing contracts. These will be deleted to allow plane model replacement.'
                )
                response = input('Do you want to continue? This will delete all existing contracts! (y/N): ')
                if response.lower() != 'y':
                    print('Import cancelled by user.')
                    return False

            # Temporarily disable foreign key constraints
            await db.execute('PRAGMA foreign_keys = OFF')

            # Clear existing contracts first (if any)
            if contract_count > 0:
                print('Clearing existing contracts...')
                await db.execute('DELETE FROM contracts')
                # Reset contracts auto-increment counter if sqlite_sequence table exists
                cursor = await db.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='sqlite_sequence'"
                )
                if await cursor.fetchone():
                    await db.execute('DELETE FROM sqlite_sequence WHERE name = "contracts"')

            # Check current schema and migrate if necessary
            print('Checking database schema...')
            cursor = await db.execute('PRAGMA table_info(plane_models)')
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]

            has_type_column = 'type' in column_names
            has_capacity_column = 'capacity' in column_names
            has_speed_column = 'speed' in column_names

            if not has_type_column or has_capacity_column or has_speed_column:
                print('Schema migration required. Recreating plane_models table...')

                # Drop the old table
                await db.execute('DROP TABLE IF EXISTS plane_models')

                # Create the new table with correct schema
                await db.execute("""
                    CREATE TABLE plane_models (
                        id INTEGER PRIMARY KEY,
                        model_name VARCHAR(50) UNIQUE NOT NULL,
                        level CHAR(1) CHECK (level IN ('A','B','C','D','E','F','G')),
                        type VARCHAR(10) CHECK (type IN ('PAX','CARGO'))
                    )
                """)
                print('New plane_models table created with updated schema')
            else:
                # Clear existing plane models data
                print('Clearing existing plane models data...')
                await db.execute('DELETE FROM plane_models')

                # Reset the auto-increment counter if sqlite_sequence table exists
                cursor = await db.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='sqlite_sequence'"
                )
                if await cursor.fetchone():
                    await db.execute('DELETE FROM sqlite_sequence WHERE name = "plane_models"')

            # Insert new data
            print('Inserting new plane models data...')
            insert_query = """
                INSERT INTO plane_models (model_name, level, type)
                VALUES (?, ?, ?)
            """

            for plane in planes_data:
                await db.execute(insert_query, (plane['model_name'], plane['level'], plane['type']))

            # Re-enable foreign key constraints
            await db.execute('PRAGMA foreign_keys = ON')

            # Commit changes
            await db.commit()

            # Verify the import
            cursor = await db.execute('SELECT COUNT(*) as count FROM plane_models')
            count_result = await cursor.fetchone()
            imported_count = count_result[0] if count_result else 0

            print(f'Successfully imported {imported_count} plane models')

            # Show sample of imported data
            print('\nSample of imported data:')
            cursor = await db.execute("""
                SELECT model_name, level, type
                FROM plane_models
                ORDER BY level, model_name
                LIMIT 10
            """)
            sample_rows = await cursor.fetchall()

            print('Model Name | Level | Type')
            print('-' * 30)
            for row in sample_rows:
                print(f'{row[0]:<10} | {row[1]:<5} | {row[2]}')

            if imported_count > 10:
                print(f'... and {imported_count - 10} more')

            return True

    except Exception as e:
        print(f'Database error: {e}')
        return False


async def main():
    """Main function to run the import."""
    print('Starting plane models import from CSV...')
    print('=' * 50)

    success = await import_planes_from_csv()

    print('=' * 50)
    if success:
        print('Import completed successfully!')
    else:
        print('Import failed!')
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
