from typing import List

import aiosqlite
from fastapi import APIRouter, Depends, HTTPException

from ..database import get_db
from ..models import PlaneModel

router = APIRouter()


@router.get('/', response_model=List[PlaneModel])
async def get_plane_models(db: aiosqlite.Connection = Depends(get_db)):
    """Get all plane models"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM plane_models ORDER BY level, model_name')
    rows = await cursor.fetchall()
    return [dict(row) for row in rows]


@router.get('/{plane_id}', response_model=PlaneModel)
async def get_plane_model(plane_id: int, db: aiosqlite.Connection = Depends(get_db)):
    """Get plane model by ID"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM plane_models WHERE id = ?', (plane_id,))
    row = await cursor.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail='Plane model not found')

    return dict(row)


@router.get('/level/{level}')
async def get_planes_by_level(level: str, db: aiosqlite.Connection = Depends(get_db)):
    """Get plane models by level (A-G)"""
    if level.upper() not in ['A', 'B', 'C', 'D', 'E', 'F', 'G']:
        raise HTTPException(status_code=400, detail='Invalid plane level. Must be A-G')

    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM plane_models WHERE level = ? ORDER BY model_name', (level.upper(),))
    rows = await cursor.fetchall()
    return [dict(row) for row in rows]
