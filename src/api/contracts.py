from typing import List, Optional

import aiosqlite
from fastapi import APIRouter, Depends, HTTPException

from ..database import get_db
from ..models import Contract, ContractCreate, ContractWithDetails

router = APIRouter()


@router.post('/', response_model=Contract)
async def create_contract(contract: ContractCreate, db: aiosqlite.Connection = Depends(get_db)):
    """Create a new contract"""
    # Verify plane model exists
    cursor = await db.execute('SELECT id FROM plane_models WHERE id = ?', (contract.plane_model_id,))
    if not await cursor.fetchone():
        raise HTTPException(status_code=400, detail='Plane model not found')

    # Verify from_airport exists
    cursor = await db.execute('SELECT id FROM airports WHERE id = ?', (contract.from_airport,))
    if not await cursor.fetchone():
        raise HTTPException(status_code=400, detail='From airport not found')

    # Verify to_airport exists
    cursor = await db.execute('SELECT id FROM airports WHERE id = ?', (contract.to_airport,))
    if not await cursor.fetchone():
        raise HTTPException(status_code=400, detail='To airport not found')

    # Insert contract
    cursor = await db.execute(
        """
        INSERT INTO contracts (plane_model_id, from_player, to_player, from_airport, to_airport, status, acceptance_status)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """,
        (
            contract.plane_model_id,
            contract.from_player,
            contract.to_player,
            contract.from_airport,
            contract.to_airport,
            contract.status,
            contract.acceptance_status,
        ),
    )

    await db.commit()

    # Return the created contract
    contract_id = cursor.lastrowid
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM contracts WHERE id = ?', (contract_id,))
    row = await cursor.fetchone()

    return dict(row)


@router.get('/', response_model=List[ContractWithDetails])
async def get_contracts(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    player_id: Optional[str] = None,
    db: aiosqlite.Connection = Depends(get_db),
):
    """Get contracts with optional filtering"""
    db.row_factory = aiosqlite.Row

    query = """
        SELECT
            c.id, c.contract_date, c.status, c.acceptance_status, c.from_player, c.to_player,
            pm.id as plane_id, pm.model_name, pm.level, pm.type,
            fa.id as from_airport_id, fa.code as from_code, fa.name as from_name, fa.region as from_region,
            ta.id as to_airport_id, ta.code as to_code, ta.name as to_name, ta.region as to_region
        FROM contracts c
        JOIN plane_models pm ON c.plane_model_id = pm.id
        JOIN airports fa ON c.from_airport = fa.id
        JOIN airports ta ON c.to_airport = ta.id
    """

    conditions = []
    params = []

    if status:
        conditions.append('c.status = ?')
        params.append(status)

    if player_id:
        conditions.append('(c.from_player = ? OR c.to_player = ?)')
        params.extend([player_id, player_id])

    if conditions:
        query += ' WHERE ' + ' AND '.join(conditions)

    query += ' ORDER BY c.contract_date DESC LIMIT ? OFFSET ?'
    params.extend([limit, skip])

    cursor = await db.execute(query, params)
    rows = await cursor.fetchall()

    result = []
    for row in rows:
        result.append({
            'id': row['id'],
            'contract_date': row['contract_date'],
            'status': row['status'],
            'acceptance_status': row['acceptance_status'],
            'from_player': row['from_player'],
            'to_player': row['to_player'],
            'plane_model': {
                'id': row['plane_id'],
                'model_name': row['model_name'],
                'level': row['level'],
                'type': row['type'],
            },
            'from_airport': {
                'id': row['from_airport_id'],
                'code': row['from_code'],
                'name': row['from_name'],
                'region': row['from_region'],
            },
            'to_airport': {
                'id': row['to_airport_id'],
                'code': row['to_code'],
                'name': row['to_name'],
                'region': row['to_region'],
            },
        })

    return result


@router.get('/{contract_id}', response_model=Contract)
async def get_contract(contract_id: int, db: aiosqlite.Connection = Depends(get_db)):
    """Get contract by ID"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM contracts WHERE id = ?', (contract_id,))
    row = await cursor.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail='Contract not found')

    return dict(row)


@router.patch('/{contract_id}/status')
async def update_contract_status(contract_id: int, status: str, db: aiosqlite.Connection = Depends(get_db)):
    """Update contract status"""
    if status not in ['active', 'completed', 'cancelled']:
        raise HTTPException(status_code=400, detail='Invalid status')

    cursor = await db.execute('UPDATE contracts SET status = ? WHERE id = ?', (status, contract_id))

    if cursor.rowcount == 0:
        raise HTTPException(status_code=404, detail='Contract not found')

    await db.commit()
    return {'message': 'Contract status updated successfully'}


@router.patch('/{contract_id}/acceptance')
async def update_contract_acceptance(
    contract_id: int, acceptance_status: str, db: aiosqlite.Connection = Depends(get_db)
):
    """Update contract acceptance status"""
    if acceptance_status not in ['pending', 'accepted', 'rejected']:
        raise HTTPException(status_code=400, detail='Invalid acceptance status')

    cursor = await db.execute(
        'UPDATE contracts SET acceptance_status = ? WHERE id = ?', (acceptance_status, contract_id)
    )

    if cursor.rowcount == 0:
        raise HTTPException(status_code=404, detail='Contract not found')

    await db.commit()
    return {'message': 'Contract acceptance status updated successfully'}


@router.delete('/{contract_id}')
async def delete_contract(contract_id: int, db: aiosqlite.Connection = Depends(get_db)):
    """Delete a contract"""
    cursor = await db.execute('DELETE FROM contracts WHERE id = ?', (contract_id,))

    if cursor.rowcount == 0:
        raise HTTPException(status_code=404, detail='Contract not found')

    await db.commit()
    return {'message': 'Contract deleted successfully'}
