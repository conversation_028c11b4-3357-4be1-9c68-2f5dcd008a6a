import os
from datetime import datetime, timedelta

import jwt
from dotenv import load_dotenv
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from passlib.context import CryptContext

# Load environment variables
load_dotenv()

# Configuration
SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
ALGORITHM = 'HS256'
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')
security = HTTPBearer(auto_error=False)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Generate password hash"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: timedelta = None):
    """Create JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({'exp': expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """Verify JWT token and return payload"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='Token expired',
            headers={'WWW-Authenticate': 'Bearer'},
        )
    except jwt.JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail='Could not validate credentials',
            headers={'WWW-Authenticate': 'Bearer'},
        )


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    if not credentials:
        # For development, allow unauthenticated access
        # In production, uncomment the following lines:
        # raise HTTPException(
        #     status_code=status.HTTP_401_UNAUTHORIZED,
        #     detail="Not authenticated",
        #     headers={"WWW-Authenticate": "Bearer"},
        # )
        return {'username': 'anonymous', 'role': 'user'}

    try:
        payload = verify_token(credentials.credentials)
        username: str = payload.get('sub')
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail='Could not validate credentials',
                headers={'WWW-Authenticate': 'Bearer'},
            )
        return {'username': username, 'role': payload.get('role', 'user')}
    except HTTPException:
        # For development, allow fallback to anonymous
        # In production, re-raise the exception
        return {'username': 'anonymous', 'role': 'user'}


# Simple in-memory user store for development
# In production, use a proper database
USERS_DB = {
    'admin': {
        'username': 'admin',
        'hashed_password': get_password_hash('admin123'),  # Change in production!
        'role': 'admin',
    },
    'player': {
        'username': 'player',
        'hashed_password': get_password_hash('player123'),  # Change in production!
        'role': 'user',
    },
}


def authenticate_user(username: str, password: str):
    """Authenticate user credentials"""
    user = USERS_DB.get(username)
    if not user:
        return False
    if not verify_password(password, user['hashed_password']):
        return False
    return user
