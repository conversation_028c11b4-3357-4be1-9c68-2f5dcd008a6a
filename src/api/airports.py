from typing import List

import aiosqlite
from fastapi import APIRouter, Depends, HTTPException

from ..database import get_db
from ..models import Airport

router = APIRouter()


@router.get('/', response_model=List[Airport])
async def get_airports(db: aiosqlite.Connection = Depends(get_db)):
    """Get all airports"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM airports ORDER BY code')
    rows = await cursor.fetchall()
    return [dict(row) for row in rows]


@router.get('/{airport_id}', response_model=Airport)
async def get_airport(airport_id: int, db: aiosqlite.Connection = Depends(get_db)):
    """Get airport by ID"""
    db.row_factory = aiosqlite.Row
    cursor = await db.execute('SELECT * FROM airports WHERE id = ?', (airport_id,))
    row = await cursor.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail='Airport not found')

    return dict(row)
